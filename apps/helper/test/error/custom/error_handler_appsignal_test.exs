defmodule Helper.Error.Custom.ErrorHandlerAppsignalTest do
  use ExUnit.Case, async: true

  describe "Module name generation" do
    test "generates valid module name from normal module" do
      context = %{
        name: AthenaWeb.Resolvers.Tracking.SendHubspotCustomEvent,
        metadata: %{}
      }

      # Mock Appsignal functions to avoid dependencies
      defmodule MockAppsignal do
        def send_error(_kind, _exception, _stack, _fun), do: :ok
      end

      defmodule MockAppsignalSpan do
        def set_namespace(span, _namespace), do: span
        def set_name(span, _name), do: span
        def set_attribute(span, _key, _value), do: span
      end

      # Test that the module name generation doesn't crash
      # We can't easily test the full function due to Appsignal dependencies,
      # but we can test the core logic
      module_name =
        context.name
        |> to_string()
        |> String.replace(~r/^Elixir\./, "")
        |> String.replace(~r/[^a-zA-Z0-9]/, "")
        |> String.trim()
        |> String.capitalize()

      assert module_name == "Athenawebresolverstrackingsendhubspotcustomevent"

      # Test that this can be used as a valid module name
      dynamic_exception_code = """
      defmodule #{module_name} do
         defexception message: "default error"
       end
      """

      assert [{dynamic_error_module, _binary}] = Code.compile_string(dynamic_exception_code)
      assert is_atom(dynamic_error_module)
    end

    test "generates valid module name from problematic lowercase module string" do
      # This simulates the problematic case that was causing the error
      problematic_name = "Elixir.athenaweb.resolvers.tracking.sendhubspotcustomevent"

      module_name =
        problematic_name
        |> String.replace(~r/^Elixir\./, "")
        |> String.replace(~r/[^a-zA-Z0-9]/, "")
        |> String.trim()
        |> String.capitalize()

      assert module_name == "Athenawebresolverstrackingsendhubspotcustomevent"

      # Test that this can be used as a valid module name
      dynamic_exception_code = """
      defmodule #{module_name}Test do
         defexception message: "default error"
       end
      """

      assert [{dynamic_error_module, _binary}] = Code.compile_string(dynamic_exception_code)
      assert is_atom(dynamic_error_module)
    end

    test "handles edge cases in module name generation" do
      test_cases = [
        {"Elixir.Simple", "Simple"},
        {"Elixir.With.Dots", "Withdots"},
        {"Elixir.with.lowercase", "Withlowercase"},
        {"Elixir.With123Numbers", "With123numbers"},
        {"Elixir.With-Special@Characters!", "Withspecialcharacters"}
      ]

      for {input, expected} <- test_cases do
        result =
          input
          |> String.replace(~r/^Elixir\./, "")
          |> String.replace(~r/[^a-zA-Z0-9]/, "")
          |> String.trim()
          |> String.capitalize()

        assert result == expected, "Expected #{input} to become #{expected}, got #{result}"

        # Verify it's a valid module name
        dynamic_exception_code = """
        defmodule #{result}EdgeCase do
           defexception message: "default error"
         end
        """

        assert [{_module, _binary}] = Code.compile_string(dynamic_exception_code)
      end
    end
  end
end
