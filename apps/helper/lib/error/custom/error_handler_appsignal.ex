defmodule Helper.Error.Custom.ErrorHandlerAppsignal do
  @moduledoc """
  Production implementation for generic handler for critical errors that require both Logger
  and AppSignal notifications
  """
  @behaviour Helper.Error.Custom.ErrorHandler

  require Logger

  @default_context %{kind: :error, name: "Eli<PERSON>rError", stack: [], metadata: %{}, namespace: :http_request}

  @impl true
  def handle(message, context, error) do
    context = Map.merge(@default_context, context)

    module_name =
      context.name
      |> to_string()
      |> String.replace(~r/^Elixir\./, "")
      |> String.replace(~r/[^a-zA-Z0-9]/, "")
      |> String.trim()
      |> String.capitalize()

    dynamic_exception_code = """
    defmodule #{module_name} do
       defexception message: "default error"
     end
    """

    [{dynamic_error_module, _binary}] = Code.compile_string(dynamic_exception_code)

    Logger.error("Notifying AppSignal about #{message}, #{inspect(context)}")

    fun = build_span_function(context)

    Appsignal.send_error(
      context.kind,
      dynamic_error_module.exception(message: "#{message} - #{inspect(error)}"),
      context.stack,
      fun
    )

    :ok
  end

  defp build_span_function(%{fun: fun}), do: fun

  defp build_span_function(context) do
    fn span ->
      span
      |> Appsignal.Span.set_namespace(context.namespace)
      |> Appsignal.Span.set_name(context.name)
      |> add_metadata_to_span(context.metadata)
    end
  end

  defp add_metadata_to_span(span, metadata) do
    Enum.reduce(metadata, span, fn {key, value}, acc ->
      Appsignal.Span.set_attribute(acc, inspect(key), inspect(value))
    end)
  end
end
