---
name: <PERSON>uild and Push Image to GCP
on:
  push:
    branches:
      - master
      - staging
  workflow_dispatch:

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-push-gcp:
    name: Build and Push to G<PERSON>
    runs-on: blacksmith-4vcpu-ubuntu-2204
    permissions:
      contents: "read"
      id-token: "write"
    env:
      IMAGE_NAME: leaf_be
      PROJECT_ID: leaf-343323
    outputs:
      version: ${{ steps.deploy.outputs.version_id }}
      environment: ${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}
    steps:
      - name: Set environment for branch
        id: branch_check
        run: |
          if [[ $GITHUB_REF == 'refs/heads/master' ]]; then
              echo "MIX_ENV=prod" >> $GITHUB_OUTPUT
              echo "LEAF_ENVIRONMENT=production" >> $GITHUB_OUTPUT
          else
              echo "MIX_ENV=prod" >> $GITHUB_OUTPUT
              echo "LEAF_ENVIRONMENT=staging" >> $GITHUB_OUTPUT
          fi

      - name: Checkout
        uses: actions/checkout@v4

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.SERVICE_ACCOUNT_KEY }}"
          project_id: ${{ env.PROJECT_ID }}

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Configure Docker Client
        run: |-
          gcloud auth configure-docker australia-southeast1-docker.pkg.dev --quiet

      - name: Build Docker Image
        run: docker build -t $IMAGE_NAME:latest . --build-arg MIX_ENV=${{ steps.branch_check.outputs.MIX_ENV }} --build-arg LEAF_ENVIRONMENT=${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}

      - name: Push Docker Image to Artifact Registry
        run: |-
          docker tag $IMAGE_NAME:latest australia-southeast1-docker.pkg.dev/${PROJECT_ID}/leaf-gae/${IMAGE_NAME}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest
          docker push australia-southeast1-docker.pkg.dev/${PROJECT_ID}/leaf-gae/${IMAGE_NAME}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest

      - name: Prune
        id: prune
        run: |-
          bash ./.cloudbuild/prune.sh ${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }} 10

      - name: Deploy to App Engine
        id: deploy
        uses: "google-github-actions/deploy-appengine@v2"
        with:
          deliverables: ".cloudbuild/app.${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}.yaml"
          image_url: australia-southeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/leaf-gae/${{ env.IMAGE_NAME }}_${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}:latest
          version: "${{ github.ref_name }}-${{ github.run_number }}-${{ github.run_attempt }}"
          flags: "--stop-previous-version"

  post-deployment:
    name: Verify and Cleanup Deployment
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs: build-push-gcp
    permissions:
      contents: "read"
      id-token: "write"
    env:
      PROJECT_ID: leaf-343323
    steps:
      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.SERVICE_ACCOUNT_KEY }}"
          project_id: ${{ env.PROJECT_ID }}

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Verify deployment is serving with traffic
        run: |
          CURRENT_VERSION="${{ needs.build-push-gcp.outputs.version }}"
          if [ "${{ needs.build-push-gcp.outputs.environment }}" = "staging" ]; then
            SERVICE="leaf-staging"
          else
            SERVICE="default"
          fi

          echo "::group::Verifying deployment for service: $SERVICE, version: $CURRENT_VERSION"

          # Wait up to 5 minutes for the version to be serving with traffic
          timeout=300
          elapsed=0
          while [ $elapsed -lt $timeout ]; do
            # Check if current version is serving and has traffic allocation
            VERSION_INFO=$(gcloud app versions list --service="$SERVICE" --filter="version.id=$CURRENT_VERSION" --format="value(SERVING_STATUS,TRAFFIC_SPLIT)" 2>/dev/null || echo "")

            if [ -n "$VERSION_INFO" ]; then
              SERVING_STATUS=$(echo "$VERSION_INFO" | cut -d$'\t' -f1)
              TRAFFIC_SPLIT=$(echo "$VERSION_INFO" | cut -d$'\t' -f2)

              echo "::debug::Version $CURRENT_VERSION - Serving Status: $SERVING_STATUS, Traffic: $TRAFFIC_SPLIT"

              if [ "$SERVING_STATUS" = "SERVING" ] && [ "$TRAFFIC_SPLIT" != "0" ] && [ -n "$TRAFFIC_SPLIT" ]; then
                echo "::notice title=Deployment Verified::Version $CURRENT_VERSION is serving and receiving traffic ($TRAFFIC_SPLIT)"
                echo "::endgroup::"
                break
              fi
            fi

            echo "::debug::Waiting for version to be serving with traffic... (${elapsed}s/${timeout}s)"
            sleep 10
            elapsed=$((elapsed + 10))
          done

          if [ $elapsed -ge $timeout ]; then
            echo "::endgroup::"
            echo "::error title=Deployment Verification Failed::Version $CURRENT_VERSION is not serving with traffic after 5 minutes"
            exit 1
          fi

      - name: Cleanup old App Engine versions
        run: |
          CURRENT_VERSION="${{ needs.build-push-gcp.outputs.version }}"
          if [ "${{ needs.build-push-gcp.outputs.environment }}" = "staging" ]; then
            SERVICE="leaf-staging"
          else
            SERVICE="default"
          fi

          echo "Cleaning App Engine versions for service: $SERVICE"
          echo "Current deployed version: $CURRENT_VERSION"
          # List all App Engine versions with no allocated traffic for the specified service and with serving status as SERVING
          gcloud app versions list --service=$SERVICE --format="value(version.id,traffic_split)" --filter="traffic_split=0 AND version.servingStatus=SERVING" | while read -r version traffic; do
            if [ "$version" != "$CURRENT_VERSION" ]; then
              echo "Stopping version $version with no allocated traffic"
              gcloud app versions stop "$version" --quiet
            fi
          done

      - name: Generate Job Summary
        if: always()
        run: |
          CURRENT_VERSION="${{ needs.build-push-gcp.outputs.version }}"
          if [ "${{ needs.build-push-gcp.outputs.environment }}" = "staging" ]; then
            SERVICE="leaf-staging"
          else
            SERVICE="default"
          fi

          echo "# 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## 📋 Build Information" >> $GITHUB_STEP_SUMMARY
          echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| **Environment** | \`${{ needs.build-push-gcp.outputs.environment }}}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| **Commit** | [\`${{ github.sha }}\`](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}) |" >> $GITHUB_STEP_SUMMARY
          echo "| **Actor** | \`${{ github.actor }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| **Workflow Run** | [\`#${{ github.run_number }}\`](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}) |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "## 🎯 Deployment Details" >> $GITHUB_STEP_SUMMARY
          echo "| Field | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| **Service** | \`$SERVICE\` |" >> $GITHUB_STEP_SUMMARY
          echo "| **Version** | \`$CURRENT_VERSION\` |" >> $GITHUB_STEP_SUMMARY
          echo "| **Image** | \`australia-southeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/leaf-gae/${{ env.IMAGE_NAME }}_${{ needs.build-push-gcp.outputs.environment }}}:latest\` |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ job.status }}" = "success" ]; then
            echo "## ✅ Deployment Status: **SUCCESS**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "🎉 Leaf is now serving traffic!" >> $GITHUB_STEP_SUMMARY
          else
            echo "## ❌ Deployment Status: **FAILED**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "⚠️ The deployment encountered issues. Please check the logs for details." >> $GITHUB_STEP_SUMMARY
          fi

      # - name: Send notification to slack on deployment failure
      #   if: always() && (job.status == 'failure')
      #   id: slack
      #   uses: slackapi/slack-github-action@v1.18.0
      #   with:
      #     payload: |
      #       {
      #         "environment": "${{ steps.branch_check.outputs.LEAF_ENVIRONMENT }}",
      #         "actor": "${{ github.actor }}",
      #         "slack-message": "GitHub build result: ${{ job.status }}\n${{ github.event.pull_request.html_url || github.event.head_commit.url }}"
      #       }
      #   env:
      #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
